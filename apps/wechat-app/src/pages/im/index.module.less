.tabPage {
    display: flex;
    flex-direction: column;
    height: 100%;
    height: 100dvh;
    background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiContainerBg.png');
    background-color: #f2f6ff;
    background-size: 100% 56%;
    background-repeat: no-repeat;
}

.tabPageIm {
    background-image:
        url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiContainerBg.png'),
        linear-gradient(180deg, #f2f6ff 0%, #e1eaff 100%);
    background-color: #f2f6ff;
    background-size:
        100% 56%,
        100% 636px;
    background-repeat: no-repeat;
    background-position: top, bottom;
}

.tabContent {
    flex: 1;
    overflow-y: auto;
}

.tabPanel {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    height: 100%;
    overflow: hidden;
}

.visible {
    display: block;
    opacity: 1;
}

.hidden {
    display: none;
}
