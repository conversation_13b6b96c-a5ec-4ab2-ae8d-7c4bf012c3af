import {PropsWithChildren} from 'react';
import {useLaunch} from '@tarojs/taro';
import {pageLeave, weappInitPage} from '@baidu/mika-ubc';

import {ubcFn} from '../../../packages/pages-im/src/utils/generalFunction/ubc';

import './app.less';

function App({children}: PropsWithChildren<any>) {
    useLaunch(info => {
        // ubc 埋点初始化注册
        ubcFn.init(info);
        // mika ubc 时长埋点初始化注册
        pageLeave.init(info);
        process.env.TARO_ENV === 'weapp' && weappInitPage();
    });

    // children 是将要会渲染的页面
    return children;
}

export default App;
