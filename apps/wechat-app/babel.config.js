// babel-preset-taro 更多选项和默认值：
// https://docs.taro.zone/docs/next/babel-config
module.exports = {
    presets: [
        [
            'taro',
            {
                framework: 'react',
                ts: true,
                compiler: 'webpack5',
                useBuiltIns: process.env.TARO_ENV === 'h5' ? 'usage' : false
            }
        ]
    ],
    plugins: [
        'lodash',
        [
            'import',
            {
                libraryName: '@baidu/health-ui',
                libraryDirectory: 'lib/components',
                camel2DashComponentName: false,
                style: true
            },
            '@baidu/health-ui'
        ],
        [
            'import',
            {
                libraryName: '@baidu/wz-taro-tools-icons',
                libraryDirectory: 'tsx',
                camel2DashComponentName: false,
                style: false
            },
            '@baidu/wz-taro-tools-icons'
        ],
        [
            'import',
            {
                libraryName: '@baidu/wz-taro-tools-core',
                libraryDirectory: '',
                style: true
            },
            '@baidu/wz-taro-tools-core'
        ]
    ]
};
