import {useRef, useCallback, useEffect} from 'react';
import {useAtomValue} from 'jotai';
import {useGetSessionId} from '../triageStream/pageDataController';
import {npsConfigAtom} from '../../store/triageStreamAtom/otherData';
import {
    getTriageStreamMsgDataByKey,
    updateCurSessionMsgDataAndMsgIds
} from '../../store/triageStreamAtom/msg';
import {useGetSessionMsgIds} from '../triageStream/dataController';
import {isEmpty} from '../../utils';
import {getNpsStorageInfoSync, setNpsStorageSync} from '../../utils/npsStorage';
import {getNpsInfo} from '../../models/services/nps/getNpsInfo';

export const useNpsIm = () => {
    const curSessionId = useGetSessionId();
    const npsConfig = useAtomValue(npsConfigAtom);
    const timerRef = useRef<ReturnType<typeof setTimeout> | null>();
    const {msgIds} = useGetSessionMsgIds();
    // 发送NPS消息的逻辑
    const sendNpsMessage = useCallback(async () => {
        if (!curSessionId || isEmpty(npsConfig?.npsInactivityDelay)) {
            return;
        }

        const npsStorage = getNpsStorageInfoSync({
            triggerType: 2,
            appraiseIdArr: npsConfig?.npsInactivityDelay
        });

        const currentDate = new Date().getTime();
        // 没有埋过NPS或npsStorage中可用appraiseIds存在
        if (
            !npsStorage ||
            (npsStorage?.availableAppraiseIds?.length &&
                currentDate - npsStorage?.createTime > 24 * 60 * 60 * 1000)
        ) {
            // 发送NPS请求
            const npsInactivityDelay = npsConfig?.npsInactivityDelay || [];
            const delayTime = npsInactivityDelay[0]?.delayTime;
            timerRef.current && clearTimeout(timerRef.current);

            if (delayTime) {
                timerRef.current = setTimeout(async () => {
                    const [err, data] = await getNpsInfo({
                        triggerType: 2,
                        sessionId: curSessionId,
                        appraiseIds: npsConfig?.npsInactivityDelay
                            ?.map(item => item?.appraiseId)
                            ?.join(',')
                    });

                    if (!err && data?.data?.hasNps === 1 && !isEmpty(data?.data?.npsMsg)) {
                        const npsMsg = data?.data?.npsMsg || {};
                        const {msgId, msgData} = npsMsg;

                        // 更新msgIds
                        msgId &&
                            msgData &&
                            updateCurSessionMsgDataAndMsgIds({
                                ids: msgId || [],
                                msgData: msgData || {},
                                ops: {type: 'push', sourceApi: 'conversation'},
                                targetSessionId: curSessionId || ''
                            });

                        // 更新npsStorage
                        setNpsStorageSync({
                            triggerType: 2,
                            curAppraiseId: data?.data?.appraiseId
                        });
                    }
                }, delayTime * 1000);
            }
        }
    }, [curSessionId, npsConfig?.npsInactivityDelay]);

    // 进页面兼容
    useEffect(() => {
        sendNpsMessage();

        // 清理effect时清除定时器
        return () => {
            timerRef.current && clearTimeout(timerRef.current);
        };
    }, [npsConfig?.npsInactivityDelay, sendNpsMessage]);

    // 监听msgIds变化的effect
    useEffect(() => {
        if (msgIds) {
            const lastMsgId = msgIds[msgIds.length - 1];
            const lastMsgData = getTriageStreamMsgDataByKey(`${curSessionId}_${lastMsgId}`);
            if (JSON.stringify(lastMsgData)) {
                // msgIds变更了 清除定时器
                timerRef.current && clearTimeout(timerRef.current);
                // 不等于bot的thinking，重新设置定时器
                lastMsgData?.data?.content?.cardName !== 'ImThinking' && sendNpsMessage();
            }
        }
    }, [curSessionId, msgIds, npsConfig, sendNpsMessage]);

    return {};
};
