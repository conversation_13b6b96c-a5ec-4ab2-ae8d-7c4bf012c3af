// Author: zhang<PERSON>yu03
// Date: 2025-02-14 17:53:19
// Description: 语音Icon组件

import {View} from '@tarojs/components';
import {useMemo, memo, useEffect} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {useGetUserData, useUpdateUserData} from '../../../hooks/triageStream/pageDataController';
import {useGetSwanMsgListSceneStatus} from '../../../hooks/triageStream/useGetSwanMsgListSceneStatus';

import type {VoiceSwitch} from './index.d';
import styles from './index.module.less';

const normalSrc = 'https://med-fe.cdn.bcebos.com/vita/voiceIocn.png';
const swanMsgSrc = 'https://med-fe.cdn.bcebos.com/vita/swanMsgVoice.png';
const keyboardSrc = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/keyboard.png';

const ImTriageVoice = memo((props: VoiceSwitch) => {
    const {handleChangeIcon, isShowVoice} = props;

    const {userData} = useGetUserData();
    const {updateUserData} = useUpdateUserData();
    const {status} = useGetSwanMsgListSceneStatus();


    useEffect(() => {
        console.info('userData', userData);
    }, [userData]);

    const VoiceSwitchCom = useMemo(() => {
        if (isShowVoice) {
            return <WImage src={keyboardSrc} className={styles.voiceIcon} onClick={handleChangeIcon}/>;
        }

        // return <WiseVoicePlay size={72} onClick={handleChangeIcon} />;
        return (
            <WImage
                src={status ? swanMsgSrc : normalSrc}
                className={styles.voiceIcon}
                onClick={handleChangeIcon}
            />
        );
    }, [handleChangeIcon, isShowVoice, status]);

    if (process.env.TARO_ENV === 'weapp') {
        return (
            <CLoginButton
                isLogin={userData?.isLogin}
                wxLoginInteractionType='popup'
                onLoginSuccess={e => {
                    console.info('哈哈哈哈', e);
                    updateUserData({
                        isLogin: e.loginStatus
                    });
                    handleChangeIcon();
                }}
            >
                <View className={cx(styles.imTriageVoiceIcon)}>{VoiceSwitchCom}</View>
            </CLoginButton>
        );
    }

    return <View className={cx(styles.imTriageVoiceIcon)}>{VoiceSwitchCom}</View>;
});

ImTriageVoice.displayName = 'ImTriageVoice';

export default ImTriageVoice;
