import cx from 'classnames';
import {Popup} from '@baidu/wz-taro-tools-core';
import {RootPortal, View} from '@tarojs/components';
import {eventCenter, useDidShow} from '@tarojs/taro';
import React, {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {showToast} from '../../../pages-im/src/utils/customShowToast';

import {navigate} from '../../../pages-im/src/utils/basicAbility/commonNavigate';
import {getWechatPassUserInfo} from '../../../pages-im/src/utils/generalFunction/login';
import {reportWeAnalysisEvent} from '../reportWeAnalysisEvent/index';

import LoginButton from '../c-login-button/index.weapp';
import {wxAgreement} from '../clogin-block/index.constant';

import styles from './index.module.less';
import {CLoginPopupProps} from './index.d';

const CLoginPopup = memo((props: CLoginPopupProps) => {
    const {successCallback, failedCallback} = props;

    const bduss = getWechatPassUserInfo()?.bduss;
    const [openState, setOpenState] = useState(false);
    const [isLogin, setIsLogin] = useState<boolean>(Boolean(bduss) || false);

    const closeCallback = useCallback(() => {
        setOpenState(false);
    }, []);

    const onLoginSuccess = useCallback(() => {
        setOpenState(false);
        successCallback?.();
    }, [successCallback]);

    const onLoginFail = useCallback(() => {
        showToast({title: '登录异常', icon: 'none'});
        failedCallback?.();
    }, [failedCallback]);

    /**
     *
     * @description 生成协议链接
     */
    const genAgreements = useMemo(() => {
        return wxAgreement.info.map((i, idx) => {
            if (i.type === 'link') {
                return (
                    <View
                        key={idx}
                        onClick={() => {
                            navigate({
                                url: i.url,
                                openType: 'navigate'
                            });
                        }}
                    >
                        {i.text}
                    </View>
                );
            }

            return null;
        });
    }, []);

    /**
     *
     * @description 生成登录弹窗
     */
    const genLoginPopup = useMemo(() => {
        console.info('openState', openState, isLogin);
        if (openState && !isLogin) {
            return (
                <RootPortal>
                    <Popup
                        open
                        rounded
                        placement='bottom'
                        titleStyle={{
                            backgroundColor: '#FFF',
                            border: 'none'
                        }}
                        style={{
                            zIndex: 1150
                        }}
                        onClose={closeCallback}
                    >
                        <Popup.Close />
                        <Popup.Backdrop
                            open
                            style={{
                                zIndex: 1100,
                                backgroundColor: 'rgba(0, 0, 0, .6)'
                            }}
                        />
                        <View className={styles.container}>
                            <View className={styles.title} />
                            <View className={styles.content}>
                                <View className={styles.icon} />
                                <LoginButton
                                    isLoginPopup
                                    isLogin={false}
                                    onLoginFail={onLoginFail}
                                    onLoginSuccess={onLoginSuccess}
                                    wxLoginInteractionType='popup'
                                >
                                    <View className={styles.loginBtn}>一键登录</View>
                                </LoginButton>
                                {/* 登录态获取需要刷新问题暂时去掉 */}
                                {/* <View className={styles.otherLoginWays} onClick={toOtherLoginType}>
                                其他登录方式
                            </View> */}
                                <View className={cx(styles.des, 'wz-mb-27')}>
                                    继续操作代表您已阅读并同意
                                </View>
                                <View className={cx(styles.protocol)}>{genAgreements}</View>
                            </View>
                        </View>
                    </Popup>
                </RootPortal>
            );
        }

        return null;
    }, [isLogin, openState, onLoginFail, closeCallback, genAgreements, onLoginSuccess]);

    useDidShow(() => {
        const userInfo = getWechatPassUserInfo();
        const loginState = Boolean(userInfo?.bduss);
        setIsLogin(loginState);
        loginState && successCallback?.();
    });

    useEffect(() => {
        eventCenter.on('showWxLoginPopup', () => {
            console.info('是是是', openState);
            setOpenState(true);
        });

        // TODO 临时处理，后续需要优化；由于 CPageContainer 组件中存在 重复渲染问题导致无法初始化多个登录弹窗
        eventCenter.on('hideWxLoginPopup', () => {
            setOpenState(false);
        });

        reportWeAnalysisEvent({
            event: 'view_login_popup',
            properties: {}
        });

        return () => {
            setOpenState(false);
            // TODO 临时处理，后续需要优化；由于 CPageContainer 组件中存在 重复渲染问题导致无法初始化多个登录弹窗
            eventCenter.trigger('hideWxLoginPopup');
        };
    }, []);

    return <>{genLoginPopup}</>;
});

CLoginPopup.displayName = 'CLoginPopup';
export default CLoginPopup;
